<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- ==================== 656dp + 320dpi (设备1) ==================== -->

    <!-- 高密度大屏保持适中 -->
    <dimen name="text_size_display">40sp</dimen>
    <dimen name="text_size_headline">30sp</dimen>
    <dimen name="text_size_title">24sp</dimen>
    <dimen name="text_size_subtitle">21sp</dimen>
    <dimen name="text_size_body">18sp</dimen>
    <dimen name="text_size_body_large">19sp</dimen>
    <dimen name="text_size_caption">16sp</dimen>
    <dimen name="text_size_small">14sp</dimen>

    <!-- 标准行高 -->
    <dimen name="line_height_display">48sp</dimen>
    <dimen name="line_height_headline">38sp</dimen>
    <dimen name="line_height_title">32sp</dimen>
    <dimen name="line_height_body">26sp</dimen>

    <!-- 标准间距 -->
    <dimen name="spacing_m">24dp</dimen>
    <dimen name="spacing_ml">28dp</dimen>
    <dimen name="spacing_l">32dp</dimen>
    <dimen name="spacing_xl">48dp</dimen>
    <dimen name="spacing_xxl">56dp</dimen>
    <dimen name="spacing_xxxl">64dp</dimen>

    <!-- 标准控件 -->
    <dimen name="button_height_large">60dp</dimen>
    <dimen name="button_height_medium">56dp</dimen>
    <dimen name="button_height_small">48dp</dimen>
    <dimen name="button_min_width">120dp</dimen>
    <dimen name="button_padding_horizontal">24dp</dimen>
    <dimen name="button_corner_radius">12dp</dimen>

    <dimen name="input_height">64dp</dimen>
    <dimen name="input_height_small">56dp</dimen>
    <dimen name="input_padding_horizontal">20dp</dimen>
    <dimen name="input_corner_radius">12dp</dimen>

    <!-- 标准图标 -->
    <dimen name="icon_size_s">32dp</dimen>
    <dimen name="icon_size_m">40dp</dimen>
    <dimen name="icon_size_l">48dp</dimen>
    <dimen name="icon_size_xl">56dp</dimen>
    <dimen name="icon_size_xxl">64dp</dimen>
    <dimen name="icon_size_xxxl">80dp</dimen>

    <!-- 标准卡片 -->
    <dimen name="card_padding">24dp</dimen>
    <dimen name="card_padding_large">28dp</dimen>
    <dimen name="card_margin">20dp</dimen>
    <dimen name="card_margin_large">24dp</dimen>
    <dimen name="card_corner_radius">16dp</dimen>
    <dimen name="card_corner_radius_large">20dp</dimen>

    <!-- 标准列表项 -->
    <dimen name="list_item_height_small">72dp</dimen>
    <dimen name="list_item_height_medium">88dp</dimen>
    <dimen name="list_item_height_large">104dp</dimen>
    <dimen name="list_item_padding_horizontal">24dp</dimen>
    <dimen name="list_item_padding_vertical">16dp</dimen>

    <!-- 标准头像 -->
    <dimen name="avatar_size_m">56dp</dimen>
    <dimen name="avatar_size_l">72dp</dimen>
    <dimen name="avatar_size_xl">88dp</dimen>
    <dimen name="avatar_size_xxl">112dp</dimen>

    <!-- 布局 -->
    <dimen name="screen_padding_horizontal">32dp</dimen>
    <dimen name="screen_padding_vertical">32dp</dimen>
    <dimen name="content_padding_horizontal">24dp</dimen>
    <dimen name="section_spacing">32dp</dimen>

    <!-- Toolbar -->
    <dimen name="toolbar_height">64dp</dimen>
    <dimen name="bottom_nav_height">72dp</dimen>
    <dimen name="tab_height">56dp</dimen>

    <!-- FAB -->
    <dimen name="fab_size">64dp</dimen>
    <dimen name="fab_margin">24dp</dimen>

    <!-- 大屏特有 -->
    <dimen name="sidebar_width">280dp</dimen>
    <dimen name="detail_pane_width">400dp</dimen>
</resources>