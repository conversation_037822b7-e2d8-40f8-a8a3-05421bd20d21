<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- ==================== 文字尺寸 ==================== -->
    <dimen name="text_size_display">32sp</dimen>
    <dimen name="text_size_headline">24sp</dimen>
    <dimen name="text_size_title">20sp</dimen>
    <dimen name="text_size_subtitle">18sp</dimen>
    <dimen name="text_size_body">16sp</dimen>
    <dimen name="text_size_body_large">17sp</dimen>
    <dimen name="text_size_caption">14sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_tiny">10sp</dimen>

    <!-- ==================== 行高 ==================== -->
    <dimen name="line_height_display">40sp</dimen>
    <dimen name="line_height_headline">32sp</dimen>
    <dimen name="line_height_title">28sp</dimen>
    <dimen name="line_height_body">24sp</dimen>

    <!-- ==================== 间距系统 ==================== -->
    <dimen name="spacing_xxxs">2dp</dimen>
    <dimen name="spacing_xxs">4dp</dimen>
    <dimen name="spacing_xs">6dp</dimen>
    <dimen name="spacing_s">8dp</dimen>
    <dimen name="spacing_sm">12dp</dimen>
    <dimen name="spacing_m">16dp</dimen>
    <dimen name="spacing_ml">20dp</dimen>
    <dimen name="spacing_l">24dp</dimen>
    <dimen name="spacing_xl">32dp</dimen>
    <dimen name="spacing_xxl">40dp</dimen>
    <dimen name="spacing_xxxl">48dp</dimen>

    <!-- ==================== 控件尺寸 ==================== -->
    <!-- 按钮 -->
    <dimen name="button_height_large">56dp</dimen>
    <dimen name="button_height_medium">48dp</dimen>
    <dimen name="button_height_small">40dp</dimen>
    <dimen name="button_min_width">88dp</dimen>
    <dimen name="button_padding_horizontal">16dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>

    <!-- 输入框 -->
    <dimen name="input_height">56dp</dimen>
    <dimen name="input_height_small">48dp</dimen>
    <dimen name="input_padding_horizontal">16dp</dimen>
    <dimen name="input_corner_radius">8dp</dimen>

    <!-- ==================== 图标尺寸 ==================== -->
    <dimen name="icon_size_xxxs">12dp</dimen>
    <dimen name="icon_size_xxs">16dp</dimen>
    <dimen name="icon_size_xs">20dp</dimen>
    <dimen name="icon_size_s">24dp</dimen>
    <dimen name="icon_size_m">32dp</dimen>
    <dimen name="icon_size_l">40dp</dimen>
    <dimen name="icon_size_xl">48dp</dimen>
    <dimen name="icon_size_xxl">56dp</dimen>
    <dimen name="icon_size_xxxl">64dp</dimen>

    <!-- ==================== 卡片/容器 ==================== -->
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_elevation_raised">8dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_corner_radius_large">16dp</dimen>
    <dimen name="card_padding">16dp</dimen>
    <dimen name="card_padding_large">20dp</dimen>
    <dimen name="card_margin">12dp</dimen>
    <dimen name="card_margin_large">16dp</dimen>

    <!-- ==================== 列表项 ==================== -->
    <dimen name="list_item_height_small">56dp</dimen>
    <dimen name="list_item_height_medium">72dp</dimen>
    <dimen name="list_item_height_large">88dp</dimen>
    <dimen name="list_item_padding_horizontal">16dp</dimen>
    <dimen name="list_item_padding_vertical">12dp</dimen>
    <dimen name="list_item_spacing">8dp</dimen>

    <!-- ==================== 头像/图片 ==================== -->
    <dimen name="avatar_size_xs">24dp</dimen>
    <dimen name="avatar_size_s">32dp</dimen>
    <dimen name="avatar_size_m">48dp</dimen>
    <dimen name="avatar_size_l">64dp</dimen>
    <dimen name="avatar_size_xl">72dp</dimen>
    <dimen name="avatar_size_xxl">96dp</dimen>

    <dimen name="thumbnail_size_small">80dp</dimen>
    <dimen name="thumbnail_size_medium">120dp</dimen>
    <dimen name="thumbnail_size_large">160dp</dimen>

    <!-- ==================== 布局约束 ==================== -->
    <dimen name="screen_padding_horizontal">16dp</dimen>
    <dimen name="screen_padding_vertical">16dp</dimen>
    <dimen name="content_padding_horizontal">16dp</dimen>
    <dimen name="content_padding_vertical">12dp</dimen>
    <dimen name="max_content_width">600dp</dimen>
    <dimen name="section_spacing">24dp</dimen>

    <!-- ==================== 分割线 ==================== -->
    <dimen name="divider_height">1dp</dimen>
    <dimen name="divider_height_thick">2dp</dimen>
    <dimen name="divider_margin">16dp</dimen>

    <!-- ==================== 顶部栏/底部栏 ==================== -->
    <dimen name="toolbar_height">56dp</dimen>
    <dimen name="toolbar_elevation">4dp</dimen>
    <dimen name="bottom_nav_height">56dp</dimen>
    <dimen name="tab_height">48dp</dimen>
    <dimen name="status_bar_height">24dp</dimen>

    <!-- ==================== FAB ==================== -->
    <dimen name="fab_size">56dp</dimen>
    <dimen name="fab_size_mini">40dp</dimen>
    <dimen name="fab_margin">16dp</dimen>

    <!-- ==================== 其他 ==================== -->
    <dimen name="ripple_radius">48dp</dimen>
    <dimen name="chip_height">32dp</dimen>
    <dimen name="chip_corner_radius">16dp</dimen>
    <dimen name="progress_size">48dp</dimen>
    <dimen name="dialog_corner_radius">16dp</dimen>
</resources>