<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Adaptersw" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.Adaptersw" parent="Base.Theme.Adaptersw" />

    <!-- 自定义样式 -->
    <style name="TextAppearance.Title" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="ShapeAppearance.Circle" parent="ShapeAppearance.Material3.Corner.Full">
        <item name="cornerSize">50%</item>
    </style>
</resources>