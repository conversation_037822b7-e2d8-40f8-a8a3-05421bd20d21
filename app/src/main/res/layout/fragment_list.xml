<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingTop="@dimen/spacing_s"
        android:paddingBottom="@dimen/spacing_s"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="@integer/grid_columns" />

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/emptyLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="@dimen/icon_size_xxxl"
            android:layout_height="@dimen/icon_size_xxxl"
            android:src="@drawable/ic_empty"
            android:contentDescription="@string/empty"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_m"
            android:text="@string/no_data"
            android:textSize="@dimen/text_size_body"
            android:textColor="?android:attr/textColorSecondary" />

    </LinearLayout>

    <!-- 加载中 -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/progress_size"
        android:layout_height="@dimen/progress_size"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>