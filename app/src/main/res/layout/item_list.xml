<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/list_item_height_medium"
    android:paddingStart="@dimen/list_item_padding_horizontal"
    android:paddingEnd="@dimen/list_item_padding_horizontal"
    android:paddingTop="@dimen/list_item_padding_vertical"
    android:paddingBottom="@dimen/list_item_padding_vertical"
    android:background="?attr/selectableItemBackground">

    <!-- 头像 -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/avatarImage"
        android:layout_width="@dimen/avatar_size_m"
        android:layout_height="@dimen/avatar_size_m"
        android:scaleType="centerCrop"
        app:shapeAppearanceOverlay="@style/ShapeAppearance.Circle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:src="@drawable/ic_placeholder" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/titleText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/spacing_m"
        android:layout_marginEnd="@dimen/spacing_s"
        android:textSize="@dimen/text_size_body_large"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintEnd_toStartOf="@id/timeText"
        app:layout_constraintStart_toEndOf="@id/avatarImage"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/subtitleText"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="列表项标题" />

    <!-- 副标题 -->
    <TextView
        android:id="@+id/subtitleText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/spacing_xxs"
        android:textSize="@dimen/text_size_caption"
        android:textColor="?android:attr/textColorSecondary"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="@id/titleText"
        app:layout_constraintStart_toStartOf="@id/titleText"
        app:layout_constraintTop_toBottomOf="@id/titleText"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="副标题或描述信息" />

    <!-- 时间标签 -->
    <TextView
        android:id="@+id/timeText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/text_size_small"
        android:textColor="?android:attr/textColorTertiary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/titleText"
        tools:text="2小时前" />

</androidx.constraintlayout.widget.ConstraintLayout>