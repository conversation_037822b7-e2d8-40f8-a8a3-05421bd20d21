<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/card_margin"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="@dimen/card_elevation"
    app:strokeWidth="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/card_padding">

        <!-- 图标 -->
        <ImageView
            android:id="@+id/iconImage"
            android:layout_width="@dimen/icon_size_l"
            android:layout_height="@dimen/icon_size_l"
            android:contentDescription="@string/icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_placeholder" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/spacing_m"
            android:textSize="@dimen/text_size_title"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iconImage"
            app:layout_constraintTop_toTopOf="@id/iconImage"
            tools:text="卡片标题" />

        <!-- 副标题 -->
        <TextView
            android:id="@+id/subtitleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_xxs"
            android:textSize="@dimen/text_size_caption"
            android:textColor="?android:attr/textColorSecondary"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/titleText"
            app:layout_constraintTop_toBottomOf="@id/titleText"
            tools:text="副标题内容" />

        <!-- 描述 -->
        <TextView
            android:id="@+id/descriptionText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_s"
            android:textSize="@dimen/text_size_body"
            android:lineHeight="@dimen/line_height_body"
            android:maxLines="3"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iconImage"
            tools:text="这是卡片的描述内容，可以显示多行文字。" />

        <!-- 操作按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/actionButton"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/button_height_small"
            android:layout_marginTop="@dimen/spacing_m"
            android:text="@string/action"
            android:textSize="@dimen/text_size_body"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descriptionText" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>