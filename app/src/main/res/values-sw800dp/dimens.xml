<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- ==================== 800dp 中等平板 基础适配 ==================== -->

    <!-- 文字尺寸 -->
    <dimen name="text_size_display">48sp</dimen>
    <dimen name="text_size_headline">34sp</dimen>
    <dimen name="text_size_title">28sp</dimen>
    <dimen name="text_size_subtitle">24sp</dimen>
    <dimen name="text_size_body">20sp</dimen>
    <dimen name="text_size_body_large">21sp</dimen>
    <dimen name="text_size_caption">18sp</dimen>
    <dimen name="text_size_small">16sp</dimen>
    <dimen name="text_size_tiny">14sp</dimen>

    <!-- 行高 -->
    <dimen name="line_height_display">56sp</dimen>
    <dimen name="line_height_headline">42sp</dimen>
    <dimen name="line_height_title">36sp</dimen>
    <dimen name="line_height_body">28sp</dimen>

    <!-- 间距 -->
    <dimen name="spacing_xxxs">4dp</dimen>
    <dimen name="spacing_xxs">8dp</dimen>
    <dimen name="spacing_xs">10dp</dimen>
    <dimen name="spacing_s">16dp</dimen>
    <dimen name="spacing_sm">20dp</dimen>
    <dimen name="spacing_m">28dp</dimen>
    <dimen name="spacing_ml">32dp</dimen>
    <dimen name="spacing_l">40dp</dimen>
    <dimen name="spacing_xl">56dp</dimen>
    <dimen name="spacing_xxl">64dp</dimen>
    <dimen name="spacing_xxxl">80dp</dimen>

    <!-- 控件尺寸 -->
    <dimen name="button_height_large">64dp</dimen>
    <dimen name="button_height_medium">60dp</dimen>
    <dimen name="button_height_small">52dp</dimen>
    <dimen name="button_min_width">140dp</dimen>
    <dimen name="button_padding_horizontal">28dp</dimen>
    <dimen name="button_corner_radius">14dp</dimen>

    <dimen name="input_height">68dp</dimen>
    <dimen name="input_height_small">60dp</dimen>
    <dimen name="input_padding_horizontal">24dp</dimen>
    <dimen name="input_corner_radius">14dp</dimen>

    <!-- 图标 -->
    <dimen name="icon_size_xxxs">18dp</dimen>
    <dimen name="icon_size_xxs">24dp</dimen>
    <dimen name="icon_size_xs">28dp</dimen>
    <dimen name="icon_size_s">36dp</dimen>
    <dimen name="icon_size_m">48dp</dimen>
    <dimen name="icon_size_l">56dp</dimen>
    <dimen name="icon_size_xl">64dp</dimen>
    <dimen name="icon_size_xxl">72dp</dimen>
    <dimen name="icon_size_xxxl">96dp</dimen>

    <!-- 卡片 -->
    <dimen name="card_elevation">8dp</dimen>
    <dimen name="card_elevation_raised">12dp</dimen>
    <dimen name="card_corner_radius">20dp</dimen>
    <dimen name="card_corner_radius_large">24dp</dimen>
    <dimen name="card_padding">32dp</dimen>
    <dimen name="card_padding_large">36dp</dimen>
    <dimen name="card_margin">24dp</dimen>
    <dimen name="card_margin_large">28dp</dimen>

    <!-- 列表项 -->
    <dimen name="list_item_height_small">80dp</dimen>
    <dimen name="list_item_height_medium">96dp</dimen>
    <dimen name="list_item_height_large">112dp</dimen>
    <dimen name="list_item_padding_horizontal">32dp</dimen>
    <dimen name="list_item_padding_vertical">20dp</dimen>
    <dimen name="list_item_spacing">16dp</dimen>

    <!-- 头像/图片 -->
    <dimen name="avatar_size_xs">36dp</dimen>
    <dimen name="avatar_size_s">48dp</dimen>
    <dimen name="avatar_size_m">64dp</dimen>
    <dimen name="avatar_size_l">80dp</dimen>
    <dimen name="avatar_size_xl">96dp</dimen>
    <dimen name="avatar_size_xxl">128dp</dimen>

    <dimen name="thumbnail_size_small">120dp</dimen>
    <dimen name="thumbnail_size_medium">180dp</dimen>
    <dimen name="thumbnail_size_large">240dp</dimen>

    <!-- 布局约束 -->
    <dimen name="screen_padding_horizontal">40dp</dimen>
    <dimen name="screen_padding_vertical">40dp</dimen>
    <dimen name="content_padding_horizontal">32dp</dimen>
    <dimen name="content_padding_vertical">28dp</dimen>
    <dimen name="max_content_width">1000dp</dimen>
    <dimen name="section_spacing">40dp</dimen>

    <!-- 分割线 -->
    <dimen name="divider_height">1dp</dimen>
    <dimen name="divider_height_thick">2dp</dimen>
    <dimen name="divider_margin">24dp</dimen>

    <!-- Toolbar/导航 -->
    <dimen name="toolbar_height">72dp</dimen>
    <dimen name="toolbar_elevation">8dp</dimen>
    <dimen name="bottom_nav_height">80dp</dimen>
    <dimen name="tab_height">64dp</dimen>
    <dimen name="status_bar_height">28dp</dimen>

    <!-- FAB -->
    <dimen name="fab_size">72dp</dimen>
    <dimen name="fab_size_mini">56dp</dimen>
    <dimen name="fab_margin">32dp</dimen>

    <!-- 平板特有 -->
    <dimen name="sidebar_width">320dp</dimen>
    <dimen name="detail_pane_width">480dp</dimen>
    <dimen name="master_detail_spacing">32dp</dimen>
    <dimen name="navigation_rail_width">80dp</dimen>

    <!-- 其他 -->
    <dimen name="ripple_radius">56dp</dimen>
    <dimen name="chip_height">40dp</dimen>
    <dimen name="chip_corner_radius">20dp</dimen>
    <dimen name="progress_size">64dp</dimen>
    <dimen name="dialog_corner_radius">20dp</dimen>
</resources>