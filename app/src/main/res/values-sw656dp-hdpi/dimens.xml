<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- ==================== 656dp + 240dpi (设备2) ==================== -->

    <!-- 低密度大屏需要更大的文字 -->
    <dimen name="text_size_display">42sp</dimen>
    <dimen name="text_size_headline">32sp</dimen>
    <dimen name="text_size_title">26sp</dimen>
    <dimen name="text_size_subtitle">22sp</dimen>
    <dimen name="text_size_body">19sp</dimen>
    <dimen name="text_size_body_large">20sp</dimen>
    <dimen name="text_size_caption">17sp</dimen>
    <dimen name="text_size_small">15sp</dimen>

    <!-- 行高调整 -->
    <dimen name="line_height_display">50sp</dimen>
    <dimen name="line_height_headline">40sp</dimen>
    <dimen name="line_height_title">34sp</dimen>
    <dimen name="line_height_body">28sp</dimen>

    <!-- 间距增大 -->
    <dimen name="spacing_m">26dp</dimen>
    <dimen name="spacing_ml">30dp</dimen>
    <dimen name="spacing_l">34dp</dimen>
    <dimen name="spacing_xl">52dp</dimen>
    <dimen name="spacing_xxl">60dp</dimen>
    <dimen name="spacing_xxxl">68dp</dimen>

    <!-- 控件尺寸增大 -->
    <dimen name="button_height_large">64dp</dimen>
    <dimen name="button_height_medium">60dp</dimen>
    <dimen name="button_height_small">52dp</dimen>
    <dimen name="button_min_width">140dp</dimen>
    <dimen name="button_padding_horizontal">28dp</dimen>
    <dimen name="button_corner_radius">14dp</dimen>

    <dimen name="input_height">68dp</dimen>
    <dimen name="input_height_small">60dp</dimen>
    <dimen name="input_padding_horizontal">24dp</dimen>
    <dimen name="input_corner_radius">14dp</dimen>

    <!-- 图标增大 -->
    <dimen name="icon_size_s">36dp</dimen>
    <dimen name="icon_size_m">44dp</dimen>
    <dimen name="icon_size_l">52dp</dimen>
    <dimen name="icon_size_xl">60dp</dimen>
    <dimen name="icon_size_xxl">68dp</dimen>
    <dimen name="icon_size_xxxl">88dp</dimen>

    <!-- 卡片调整 -->
    <dimen name="card_padding">28dp</dimen>
    <dimen name="card_padding_large">32dp</dimen>
    <dimen name="card_margin">24dp</dimen>
    <dimen name="card_margin_large">28dp</dimen>
    <dimen name="card_corner_radius">18dp</dimen>
    <dimen name="card_corner_radius_large">22dp</dimen>

    <!-- 列表项增高 -->
    <dimen name="list_item_height_small">80dp</dimen>
    <dimen name="list_item_height_medium">96dp</dimen>
    <dimen name="list_item_height_large">112dp</dimen>
    <dimen name="list_item_padding_horizontal">28dp</dimen>
    <dimen name="list_item_padding_vertical">18dp</dimen>

    <!-- 头像增大 -->
    <dimen name="avatar_size_m">64dp</dimen>
    <dimen name="avatar_size_l">80dp</dimen>
    <dimen name="avatar_size_xl">96dp</dimen>
    <dimen name="avatar_size_xxl">120dp</dimen>

    <!-- 布局 -->
    <dimen name="screen_padding_horizontal">36dp</dimen>
    <dimen name="screen_padding_vertical">36dp</dimen>
    <dimen name="content_padding_horizontal">28dp</dimen>
    <dimen name="section_spacing">36dp</dimen>

    <!-- Toolbar -->
    <dimen name="toolbar_height">68dp</dimen>
    <dimen name="bottom_nav_height">76dp</dimen>
    <dimen name="tab_height">60dp</dimen>

    <!-- FAB -->
    <dimen name="fab_size">68dp</dimen>
    <dimen name="fab_margin">28dp</dimen>

    <!-- 大屏特有 -->
    <dimen name="sidebar_width">300dp</dimen>
    <dimen name="detail_pane_width">420dp</dimen>
</resources>