<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- AppBar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="@dimen/toolbar_elevation">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/toolbar_height"
            android:paddingStart="@dimen/spacing_xl"
            android:paddingEnd="@dimen/spacing_xl"
            app:title="应用标题"
            app:titleTextAppearance="@style/TextAppearance.Title" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 双栏布局 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:baselineAligned="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 左侧列表栏 -->
        <androidx.cardview.widget.CardView
            android:layout_width="@dimen/sidebar_width"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/spacing_m"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/card_elevation">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:scrollbars="vertical" />

        </androidx.cardview.widget.CardView>

        <!-- 分割线 -->
        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="?android:attr/listDivider" />

        <!-- 右侧详情栏 -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <FrameLayout
                android:id="@+id/detailContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/screen_padding_horizontal">

                <TextView
                    android:id="@+id/detailTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="详情区域"
                    android:textSize="16sp"
                    android:padding="16dp"
                    android:gravity="center" />

            </FrameLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <!-- FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="@dimen/fab_size"
        android:layout_height="@dimen/fab_size"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/fab_margin"
        app:srcCompat="@drawable/ic_add" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>