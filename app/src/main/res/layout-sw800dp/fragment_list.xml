<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/spacing_m">

    <!-- 标题栏 -->
    <TextView
        android:id="@+id/sectionTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/list_title"
        android:textSize="@dimen/text_size_title"
        android:textStyle="bold"
        android:paddingStart="@dimen/spacing_s"
        android:paddingBottom="@dimen/spacing_m"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- RecyclerView with more spacing -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingTop="@dimen/spacing_s"
        android:paddingBottom="@dimen/spacing_s"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="@integer/grid_columns"
        app:layout_constraintTop_toBottomOf="@id/sectionTitle"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 空状态 - 更大 -->
    <LinearLayout
        android:id="@+id/emptyLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/recyclerView"
        app:layout_constraintBottom_toBottomOf="@id/recyclerView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="@dimen/icon_size_xxxl"
            android:layout_height="@dimen/icon_size_xxxl"
            android:src="@drawable/ic_empty"
            android:contentDescription="@string/empty"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_l"
            android:text="@string/no_data"
            android:textSize="@dimen/text_size_subtitle"
            android:textColor="?android:attr/textColorSecondary" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/progress_size"
        android:layout_height="@dimen/progress_size"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/recyclerView"
        app:layout_constraintBottom_toBottomOf="@id/recyclerView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>