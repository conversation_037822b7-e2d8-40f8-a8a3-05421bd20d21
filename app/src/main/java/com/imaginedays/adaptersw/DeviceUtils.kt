package com.imaginedays.adaptersw

import android.content.Context
import android.content.res.Configuration
import android.util.DisplayMetrics
import android.util.TypedValue

object DeviceUtils {

    /**
     * 判断是否为平板设备
     */
    fun isTablet(context: Context): <PERSON><PERSON><PERSON> {
        return context.resources.getBoolean(R.bool.is_tablet)
    }

    /**
     * 判断是否使用双栏布局
     */
    fun useTwoPaneLayout(context: Context): Boolean {
        return context.resources.getBoolean(R.bool.use_two_pane_layout)
    }

    /**
     * 获取屏幕最小宽度(dp)
     */
    fun getSmallestWidthDp(context: Context): Int {
        return context.resources.configuration.smallestScreenWidthDp
    }

    /**
     * 获取屏幕密度DPI
     */
    fun getScreenDensityDpi(context: Context): Int {
        return context.resources.displayMetrics.densityDpi
    }

    /**
     * 判断是否为横屏
     */
    fun isLandscape(context: Context): <PERSON><PERSON>an {
        return context.resources.configuration.orientation ==
                Configuration.ORIENTATION_LANDSCAPE
    }

    /**
     * dp转px
     */
    fun dpToPx(context: Context, dp: Float): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            context.resources.displayMetrics
        ).toInt()
    }

    /**
     * sp转px
     */
    fun spToPx(context: Context, sp: Float): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP,
            sp,
            context.resources.displayMetrics
        ).toInt()
    }

    /**
     * px转dp
     */
    fun pxToDp(context: Context, px: Float): Float {
        return px / context.resources.displayMetrics.density
    }

    /**
     * 获取设备类型描述
     */
    fun getDeviceTypeDescription(context: Context): String {
        val sw = getSmallestWidthDp(context)
        val dpi = getScreenDensityDpi(context)

        return when {
            sw == 800 && dpi == 240 -> "设备3 (中等平板, 低密度)"
            sw == 656 && dpi == 320 -> "设备1 (大屏, 高密度)"
            sw == 656 && dpi == 240 -> "设备2 (大屏, 低密度)"
            sw >= 800 -> "大平板"
            sw >= 656 -> "小平板/大屏"
            sw >= 600 -> "小平板"
            else -> "手机"
        }
    }

    /**
     * 获取网格列数
     */
    fun getGridColumns(context: Context): Int {
        return context.resources.getInteger(R.integer.grid_columns)
    }
}