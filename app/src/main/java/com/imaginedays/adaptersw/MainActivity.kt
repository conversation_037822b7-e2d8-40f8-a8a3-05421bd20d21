package com.imaginedays.adaptersw

import android.content.res.Configuration
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton

class MainActivity : AppCompatActivity() {

    private val TAG = "MainActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化UI
        setupToolbar()
        setupRecyclerView()
        setupFAB()

        // 打印设备信息
        logDeviceInfo()

        // 根据设备类型调整UI
        adjustUIForDevice()
    }

    private fun setupToolbar() {
        setSupportActionBar(findViewById(R.id.toolbar))
        supportActionBar?.apply {
            title = "三设备适配示例"
            setDisplayHomeAsUpEnabled(true)
        }
    }

    private fun setupRecyclerView() {
        val recyclerView = findViewById<RecyclerView>(R.id.recyclerView) ?: return

        // 从资源获取列数
        val gridColumns = resources.getInteger(R.integer.grid_columns)

        recyclerView.apply {
            layoutManager = GridLayoutManager(this@MainActivity, gridColumns)
            // adapter = YourAdapter() // 设置你的adapter

            // 添加ItemDecoration增加间距
            val spacing = resources.getDimensionPixelSize(R.dimen.card_margin)
            addItemDecoration(GridSpacingItemDecoration(gridColumns, spacing, true))
        }

        Log.d(TAG, "RecyclerView设置为 $gridColumns 列布局")
    }

    private fun setupFAB() {
        findViewById<FloatingActionButton>(R.id.fab)?.setOnClickListener {
            // FAB点击事件
            Log.d(TAG, "FAB clicked")
        }
    }

    private fun logDeviceInfo() {
        val metrics = resources.displayMetrics
        val config = resources.configuration

        // 判断设备类型
        val deviceType = getDeviceType(config.smallestScreenWidthDp)
        val densityName = getDensityName(metrics.densityDpi)

        // 判断是哪个设备
        val deviceIdentification = identifyDevice(
            config.smallestScreenWidthDp,
            metrics.densityDpi
        )

        Log.d(TAG, """
            ========================================
            设备识别
            ========================================
            设备判定: $deviceIdentification
            设备类型: $deviceType
            
            ========================================
            屏幕参数
            ========================================
            屏幕密度: ${metrics.densityDpi} dpi ($densityName)
            密度系数: ${metrics.density}
            分辨率: ${metrics.widthPixels} × ${metrics.heightPixels} px
            最小宽度: ${config.smallestScreenWidthDp} dp
            当前宽度: ${config.screenWidthDp} dp
            当前高度: ${config.screenHeightDp} dp
            屏幕方向: ${if (config.orientation == Configuration.ORIENTATION_PORTRAIT) "竖屏" else "横屏"}
            
            ========================================
            使用的资源值
            ========================================
            text_size_body: ${getDimensionInSp(R.dimen.text_size_body)} sp
            text_size_title: ${getDimensionInSp(R.dimen.text_size_title)} sp
            spacing_m: ${getDimensionInDp(R.dimen.spacing_m)} dp
            spacing_l: ${getDimensionInDp(R.dimen.spacing_l)} dp
            button_height: ${getDimensionInDp(R.dimen.button_height_medium)} dp
            icon_size_m: ${getDimensionInDp(R.dimen.icon_size_m)} dp
            card_padding: ${getDimensionInDp(R.dimen.card_padding)} dp
            toolbar_height: ${getDimensionInDp(R.dimen.toolbar_height)} dp
            grid_columns: ${resources.getInteger(R.integer.grid_columns)}
            is_tablet: ${resources.getBoolean(R.bool.is_tablet)}
            use_two_pane: ${resources.getBoolean(R.bool.use_two_pane_layout)}
            
            ========================================
            资源文件夹匹配
            ========================================
            ${getMatchedResourceFolder()}
        """.trimIndent())
    }

    private fun identifyDevice(sw: Int, dpi: Int): String {
        return when {
            sw == 800 && dpi == 240 -> "设备3 (800dp + 240dpi)"
            sw == 656 && dpi == 320 -> "设备1 (656dp + 320dpi)"
            sw == 656 && dpi == 240 -> "设备2 (656dp + 240dpi)"
            sw >= 800 -> "其他大平板 (${sw}dp)"
            sw >= 656 -> "其他大屏设备 (${sw}dp)"
            sw >= 600 -> "小平板 (${sw}dp)"
            else -> "手机设备 (${sw}dp)"
        }
    }

    private fun getDeviceType(sw: Int): String {
        return when {
            sw >= 800 -> "大平板 (800dp+)"
            sw >= 656 -> "小平板/大屏 (656dp+)"
            sw >= 600 -> "小平板 (600dp+)"
            sw >= 411 -> "大屏手机"
            sw >= 360 -> "标准手机"
            else -> "小屏手机"
        }
    }

    private fun getDensityName(dpi: Int): String {
        return when (dpi) {
            DisplayMetrics.DENSITY_LOW -> "ldpi"
            DisplayMetrics.DENSITY_MEDIUM -> "mdpi"
            DisplayMetrics.DENSITY_HIGH -> "hdpi"
            DisplayMetrics.DENSITY_XHIGH -> "xhdpi"
            DisplayMetrics.DENSITY_XXHIGH -> "xxhdpi"
            DisplayMetrics.DENSITY_XXXHIGH -> "xxxhdpi"
            else -> "custom-${dpi}dpi"
        }
    }

    private fun getMatchedResourceFolder(): String {
        val sw = resources.configuration.smallestScreenWidthDp
        val dpi = resources.displayMetrics.densityDpi

        val densityQualifier = getDensityName(dpi)

        val swQualifier = when {
            sw >= 800 -> "sw800dp"
            sw >= 656 -> "sw656dp"
            sw >= 600 -> "sw600dp"
            sw >= 411 -> "sw411dp"
            sw >= 360 -> "sw360dp"
            else -> "default"
        }

        return """
            优先级1: values-$swQualifier-$densityQualifier/
            优先级2: values-$swQualifier/
            优先级3: values-$densityQualifier/
            后备: values/
        """.trimIndent()
    }

    private fun getDimensionInDp(resId: Int): String {
        val px = resources.getDimension(resId)
        val dp = px / resources.displayMetrics.density
        return "%.1f".format(dp)
    }

    private fun getDimensionInSp(resId: Int): String {
        val px = resources.getDimension(resId)
        val sp = px / resources.displayMetrics.scaledDensity
        return "%.1f".format(sp)
    }

    private fun adjustUIForDevice() {
        val sw = resources.configuration.smallestScreenWidthDp
        val isTablet = resources.getBoolean(R.bool.is_tablet)
        val useTwoPane = resources.getBoolean(R.bool.use_two_pane_layout)

        when {
            sw >= 800 -> {
                // 设备3 - 大平板
                Log.d(TAG, "应用大平板优化: 双栏布局")
                // 双栏布局已在layout-sw800dp中定义
            }
            sw >= 656 -> {
                // 设备1&2 - 小平板/大屏
                Log.d(TAG, "应用大屏优化: ${if (useTwoPane) "双栏" else "单栏"}布局")
                // 可选双栏布局
            }
            else -> {
                // 手机
                Log.d(TAG, "应用标准手机布局")
            }
        }

        // 动态调整其他UI元素
        if (isTablet) {
            // 平板特殊处理
            adjustForTablet()
        }
    }

    private fun adjustForTablet() {
        // 平板特殊UI调整
        Log.d(TAG, "应用平板特殊优化")

        // 例如：调整对话框尺寸
        // 例如：启用更多高级功能
        // 例如：显示更多信息等
    }
}