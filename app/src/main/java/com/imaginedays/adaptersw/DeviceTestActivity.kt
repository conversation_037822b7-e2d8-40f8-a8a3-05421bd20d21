package com.imaginedays.adaptersw


import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class DeviceTestActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_test)

        displayDeviceInfo()
    }

    private fun displayDeviceInfo() {
        val infoText = findViewById<TextView>(R.id.deviceInfoText)

        val metrics = resources.displayMetrics
        val config = resources.configuration

        val info = buildString {
            appendLine("========== 设备信息 ==========")
            appendLine("设备类型: ${DeviceUtils.getDeviceTypeDescription(this@DeviceTestActivity)}")
            appendLine()

            appendLine("========== 屏幕参数 ==========")
            appendLine("分辨率: ${metrics.widthPixels} × ${metrics.heightPixels} px")
            appendLine("密度DPI: ${metrics.densityDpi}")
            appendLine("密度系数: ${metrics.density}")
            appendLine("最小宽度: ${config.smallestScreenWidthDp} dp")
            appendLine()

            appendLine("========== 资源配置 ==========")
            appendLine("是否平板: ${DeviceUtils.isTablet(this@DeviceTestActivity)}")
            appendLine("双栏布局: ${DeviceUtils.useTwoPaneLayout(this@DeviceTestActivity)}")
            appendLine("网格列数: ${DeviceUtils.getGridColumns(this@DeviceTestActivity)}")
            appendLine()

            appendLine("========== 尺寸资源 ==========")
            appendLine("text_size_body: ${getDimen(R.dimen.text_size_body)} sp")
            appendLine("text_size_title: ${getDimen(R.dimen.text_size_title)} sp")
            appendLine("spacing_m: ${getDimenDp(R.dimen.spacing_m)} dp")
            appendLine("button_height: ${getDimenDp(R.dimen.button_height_medium)} dp")
            appendLine("icon_size_m: ${getDimenDp(R.dimen.icon_size_m)} dp")
            appendLine("card_padding: ${getDimenDp(R.dimen.card_padding)} dp")
        }

        infoText.text = info
    }

    private fun getDimen(resId: Int): String {
        val px = resources.getDimension(resId)
        val sp = px / resources.displayMetrics.scaledDensity
        return "%.1f".format(sp)
    }

    private fun getDimenDp(resId: Int): String {
        val px = resources.getDimension(resId)
        val dp = px / resources.displayMetrics.density
        return "%.1f".format(dp)
    }
}